{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="{% get_current_language as LANGUAGE_CODE %}{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "School ERP System" %}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    {% if LANGUAGE_CODE == 'ar' %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if user.is_authenticated %}
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="{% url 'accounts:dashboard' %}">
                    <i class="fas fa-school"></i> {% trans "School ERP" %}
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        {% block nav_items %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                                    <i class="fas fa-tachometer-alt"></i> {% trans "Dashboard" %}
                                </a>
                            </li>
                            
                            {% if user.user_type == 'admin' %}
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-users"></i> {% trans "Students" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#">{% trans "All Students" %}</a></li>
                                        <li><a class="dropdown-item" href="#">{% trans "Add Student" %}</a></li>
                                        <li><a class="dropdown-item" href="#">{% trans "Classes" %}</a></li>
                                    </ul>
                                </li>
                                
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-money-bill"></i> {% trans "Finance" %}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#">{% trans "Payments" %}</a></li>
                                        <li><a class="dropdown-item" href="#">{% trans "Fees" %}</a></li>
                                        <li><a class="dropdown-item" href="#">{% trans "Reports" %}</a></li>
                                    </ul>
                                </li>
                            {% endif %}
                        {% endblock %}
                    </ul>
                    
                    <ul class="navbar-nav">
                        <!-- Language Switcher -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-globe"></i>
                                {% if LANGUAGE_CODE == 'ar' %}العربية{% else %}English{% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <form method="post" action="{% url 'accounts:change_language' %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="en">
                                        <button type="submit" class="dropdown-item">English</button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="{% url 'accounts:change_language' %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="ar">
                                        <button type="submit" class="dropdown-item">العربية</button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                        
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user-edit"></i> {% trans "Profile" %}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt"></i> {% trans "Logout" %}
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main class="container-fluid mt-4">
        <!-- Messages -->
        {% if messages %}
            <div class="row">
                <div class="col-12">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-lg-start mt-5">
        <div class="text-center p-3">
            © 2025 {% trans "School ERP System" %} - {% trans "All rights reserved" %}
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
