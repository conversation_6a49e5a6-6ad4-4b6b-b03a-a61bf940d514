Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 17325
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/css/login.css HTTP/1.1" 200 951
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 29444
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 14803
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4692
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 17026
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 17492
Watching for file changes with StatReloader
Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1523, in get_connection
    connection = self._available_connections.pop()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\admin\sites.py", line 449, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\debug.py", line 143, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 89, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 144, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 108, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\__init__.py", line 187, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 432, in cycle_key
    self.create()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 55, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 196, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 117, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\base.py", line 301, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 42, in wrapper
    return panel._record_call(cache, name, original_method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 151, in _record_call
    value = original_method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 211, in has_key
    return self._cache.has_key(key)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 131, in has_key
    return bool(client.exists(key))
                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names, keys=names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1525, in get_connection
    connection = self.make_connection()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1571, in make_connection
    return self.connection_class(**self.connection_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 747, in __init__
    super().__init__(**kwargs)
TypeError: AbstractConnection.__init__() got an unexpected keyword argument 'CLIENT_CLASS'
"POST /admin/login/?next=/admin/ HTTP/1.1" 500 239671
Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1523, in get_connection
    connection = self._available_connections.pop()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\admin\sites.py", line 449, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\debug.py", line 143, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 89, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 144, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 108, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\__init__.py", line 187, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 432, in cycle_key
    self.create()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 55, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 196, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 117, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\base.py", line 301, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 42, in wrapper
    return panel._record_call(cache, name, original_method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 151, in _record_call
    value = original_method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 211, in has_key
    return self._cache.has_key(key)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 131, in has_key
    return bool(client.exists(key))
                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names, keys=names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1525, in get_connection
    connection = self.make_connection()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1571, in make_connection
    return self.connection_class(**self.connection_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 747, in __init__
    super().__init__(**kwargs)
TypeError: AbstractConnection.__init__() got an unexpected keyword argument 'CLIENT_CLASS'
"POST /admin/login/?next=/admin/ HTTP/1.1" 500 239671
Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1523, in get_connection
    connection = self._available_connections.pop()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\admin\sites.py", line 449, in login
    return LoginView.as_view(**defaults)(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\debug.py", line 143, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 89, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\base.py", line 144, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\views.py", line 108, in form_valid
    auth_login(self.request, form.get_user())
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\auth\__init__.py", line 187, in login
    request.session.cycle_key()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 432, in cycle_key
    self.create()
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 55, in create
    self._session_key = self._get_new_session_key()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\base.py", line 196, in _get_new_session_key
    if not self.exists(session_key):
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 117, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\base.py", line 301, in __contains__
    return self.has_key(key)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 42, in wrapper
    return panel._record_call(cache, name, original_method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\debug_toolbar\panels\cache.py", line 151, in _record_call
    value = original_method(*args, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 211, in has_key
    return self._cache.has_key(key)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\django\core\cache\backends\redis.py", line 131, in has_key
    return bool(client.exists(key))
                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names, keys=names)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 623, in execute_command
    return self._execute_command(*args, **options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\client.py", line 629, in _execute_command
    conn = self.connection or pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\utils.py", line 191, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1525, in get_connection
    connection = self.make_connection()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 1571, in make_connection
    return self.connection_class(**self.connection_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\Rahma-project-HBnV-8kn\Lib\site-packages\redis\connection.py", line 747, in __init__
    super().__init__(**kwargs)
TypeError: AbstractConnection.__init__() got an unexpected keyword argument 'CLIENT_CLASS'
"POST /admin/login/?next=/admin/ HTTP/1.1" 500 239671
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /accounts
"GET /accounts HTTP/1.1" 404 17014
"GET /accounts/login HTTP/1.1" 301 0
"GET /accounts/login/ HTTP/1.1" 200 21598
"GET /static/css/style.css HTTP/1.1" 200 6824
"GET /static/js/main.js HTTP/1.1" 200 11978
"GET /static/js/sw.js HTTP/1.1" 200 9601
"GET / HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 302 0
"GET /accounts/login/ HTTP/1.1" 200 21598
"GET /accounts/login/?next=/accounts/dashboard/ HTTP/1.1" 200 21624
"GET /accounts/login/ HTTP/1.1" 200 21598
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
"POST /accounts/login/ HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 200 21692
"GET /accounts/dashboard/ HTTP/1.1" 200 21258
D:\Python-projects\school-onlinepro\school_erp\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"HEAD / HTTP/1.1" 302 0
"HEAD /accounts/login/ HTTP/1.1" 200 0
- Broken pipe from ('127.0.0.1', 57692)
"GET / HTTP/1.1" 302 0
"GET /accounts/dashboard/ HTTP/1.1" 200 21258
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
"GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
"HEAD /admin/ HTTP/1.1" 302 0
"HEAD /admin/login/?next=/admin/ HTTP/1.1" 200 0
- Broken pipe from ('127.0.0.1', 57714)
"GET /accounts/dashboard/ HTTP/1.1" 200 21258
Watching for file changes with StatReloader
